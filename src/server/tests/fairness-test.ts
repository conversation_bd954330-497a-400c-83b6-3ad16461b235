/**
 * 发牌公平性测试框架
 * 用于验证修复前后房主是否还会系统性地拿到差牌
 */

import { randomHands, parse, rank } from '../utils/game-engine';
import { Card } from '../../ApiType';

interface PlayerStats {
  name: string;
  position: number; // 在users数组中的位置
  totalGames: number;
  handStrengths: number[]; // 每局的手牌强度
  averageStrength: number;
  winRate: number;
  strongHands: number; // 强牌数量 (AA, KK, QQ, JJ, AK, AQ)
  mediumHands: number; // 中等牌数量
  weakHands: number; // 弱牌数量
}

interface TestResult {
  totalGames: number;
  players: PlayerStats[];
  roomOwnerStats: PlayerStats;
  fairnessScore: number; // 0-1，1为完全公平
  pValue: number; // 统计显著性
}

/**
 * 计算手牌强度 (0-100)
 */
function calculateHandStrength(cards: Card[]): number {
  if (cards.length !== 2) return 0;
  
  const [card1, card2] = cards;
  const num1 = card1.num;
  const num2 = card2.num;
  const suited = card1.suit === card2.suit;
  
  // 对子
  if (num1 === num2) {
    if (num1 >= 14) return 95; // AA
    if (num1 >= 13) return 90; // KK
    if (num1 >= 12) return 85; // QQ
    if (num1 >= 11) return 80; // JJ
    if (num1 >= 10) return 75; // TT
    if (num1 >= 8) return 65;  // 88-99
    return 50; // 小对子
  }
  
  // 高牌组合
  const high = Math.max(num1, num2);
  const low = Math.min(num1, num2);
  
  if (high === 14) { // A开头
    if (low >= 13) return suited ? 88 : 85; // AK
    if (low >= 12) return suited ? 82 : 78; // AQ
    if (low >= 11) return suited ? 75 : 70; // AJ
    if (low >= 10) return suited ? 68 : 62; // AT
    return suited ? 55 : 45; // A小牌
  }
  
  if (high >= 13 && low >= 12) return suited ? 70 : 65; // KQ
  if (high >= 13 && low >= 11) return suited ? 65 : 60; // KJ
  if (high >= 12 && low >= 11) return suited ? 62 : 58; // QJ
  
  // 连牌
  if (Math.abs(high - low) === 1 && low >= 8) {
    return suited ? 55 : 50;
  }
  
  // 其他组合
  if (suited && high >= 10) return 45;
  if (high >= 12) return 40;
  
  return 25; // 垃圾牌
}

/**
 * 分类手牌强度
 */
function categorizeHandStrength(strength: number): 'strong' | 'medium' | 'weak' {
  if (strength >= 70) return 'strong';
  if (strength >= 50) return 'medium';
  return 'weak';
}

/**
 * 模拟游戏发牌
 */
function simulateGame(playerCount: number, roomOwnerPosition: number = 0): PlayerStats[] {
  const cards = randomHands(52);
  const players: PlayerStats[] = [];
  
  // 初始化玩家
  for (let i = 0; i < playerCount; i++) {
    players.push({
      name: i === roomOwnerPosition ? 'RoomOwner' : `Player${i}`,
      position: i,
      totalGames: 0,
      handStrengths: [],
      averageStrength: 0,
      winRate: 0,
      strongHands: 0,
      mediumHands: 0,
      weakHands: 0
    });
  }
  
  // 模拟发牌（按原始固定顺序）
  let cardIndex = 0;
  for (let i = 0; i < playerCount; i++) {
    const playerCards = [cards[cardIndex], cards[cardIndex + 1]];
    cardIndex += 2;
    
    const strength = calculateHandStrength(playerCards);
    players[i].handStrengths.push(strength);
    players[i].totalGames = 1;
    
    const category = categorizeHandStrength(strength);
    if (category === 'strong') players[i].strongHands = 1;
    else if (category === 'medium') players[i].mediumHands = 1;
    else players[i].weakHands = 1;
  }
  
  return players;
}

/**
 * 模拟游戏发牌（修复后的随机顺序）
 */
function simulateGameFixed(playerCount: number, roomOwnerPosition: number = 0): PlayerStats[] {
  const cards = randomHands(52);
  const players: PlayerStats[] = [];
  
  // 初始化玩家
  for (let i = 0; i < playerCount; i++) {
    players.push({
      name: i === roomOwnerPosition ? 'RoomOwner' : `Player${i}`,
      position: i,
      totalGames: 0,
      handStrengths: [],
      averageStrength: 0,
      winRate: 0,
      strongHands: 0,
      mediumHands: 0,
      weakHands: 0
    });
  }
  
  // 创建随机发牌顺序
  const dealOrder = Array.from({length: playerCount}, (_, i) => i);
  // Fisher-Yates洗牌
  for (let i = dealOrder.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [dealOrder[i], dealOrder[j]] = [dealOrder[j], dealOrder[i]];
  }
  
  // 按随机顺序发牌
  let cardIndex = 0;
  for (const playerIndex of dealOrder) {
    const playerCards = [cards[cardIndex], cards[cardIndex + 1]];
    cardIndex += 2;
    
    const strength = calculateHandStrength(playerCards);
    players[playerIndex].handStrengths.push(strength);
    players[playerIndex].totalGames = 1;
    
    const category = categorizeHandStrength(strength);
    if (category === 'strong') players[playerIndex].strongHands = 1;
    else if (category === 'medium') players[playerIndex].mediumHands = 1;
    else players[playerIndex].weakHands = 1;
  }
  
  return players;
}

/**
 * 运行大量测试
 */
export function runFairnessTest(
  gameCount: number = 10000, 
  playerCount: number = 4,
  useFixedVersion: boolean = false
): TestResult {
  const allPlayers: PlayerStats[] = [];
  
  // 初始化玩家统计
  for (let i = 0; i < playerCount; i++) {
    allPlayers.push({
      name: i === 0 ? 'RoomOwner' : `Player${i}`,
      position: i,
      totalGames: 0,
      handStrengths: [],
      averageStrength: 0,
      winRate: 0,
      strongHands: 0,
      mediumHands: 0,
      weakHands: 0
    });
  }
  
  // 运行测试
  for (let game = 0; game < gameCount; game++) {
    const gameResult = useFixedVersion 
      ? simulateGameFixed(playerCount, 0)
      : simulateGame(playerCount, 0);
    
    // 累积统计
    for (let i = 0; i < playerCount; i++) {
      allPlayers[i].totalGames++;
      allPlayers[i].handStrengths.push(...gameResult[i].handStrengths);
      allPlayers[i].strongHands += gameResult[i].strongHands;
      allPlayers[i].mediumHands += gameResult[i].mediumHands;
      allPlayers[i].weakHands += gameResult[i].weakHands;
    }
  }
  
  // 计算平均值
  for (const player of allPlayers) {
    player.averageStrength = player.handStrengths.reduce((a, b) => a + b, 0) / player.handStrengths.length;
  }
  
  // 计算公平性分数
  const roomOwner = allPlayers[0];
  const otherPlayers = allPlayers.slice(1);
  const avgOtherStrength = otherPlayers.reduce((sum, p) => sum + p.averageStrength, 0) / otherPlayers.length;
  
  const strengthDiff = Math.abs(roomOwner.averageStrength - avgOtherStrength);
  const fairnessScore = Math.max(0, 1 - strengthDiff / 20); // 20分差异对应0分公平性
  
  // 简单的t检验p值估算
  const pValue = strengthDiff > 2 ? 0.01 : 0.5; // 简化计算
  
  return {
    totalGames: gameCount,
    players: allPlayers,
    roomOwnerStats: roomOwner,
    fairnessScore,
    pValue
  };
}

/**
 * 打印测试结果
 */
export function printTestResult(result: TestResult, title: string): void {
  console.log(`\n=== ${title} ===`);
  console.log(`总游戏数: ${result.totalGames}`);
  console.log(`公平性分数: ${result.fairnessScore.toFixed(3)} (1.0为完全公平)`);
  console.log(`统计显著性: p=${result.pValue.toFixed(3)}`);
  
  console.log('\n玩家统计:');
  result.players.forEach(player => {
    console.log(`${player.name.padEnd(12)} | 平均牌力: ${player.averageStrength.toFixed(2)} | 强牌: ${player.strongHands} | 中等: ${player.mediumHands} | 弱牌: ${player.weakHands}`);
  });
  
  const roomOwner = result.roomOwnerStats;
  const others = result.players.filter(p => p.name !== 'RoomOwner');
  const avgOther = others.reduce((sum, p) => sum + p.averageStrength, 0) / others.length;
  
  console.log(`\n房主 vs 其他玩家:`);
  console.log(`房主平均牌力: ${roomOwner.averageStrength.toFixed(2)}`);
  console.log(`其他玩家平均: ${avgOther.toFixed(2)}`);
  console.log(`差异: ${(roomOwner.averageStrength - avgOther).toFixed(2)} ${roomOwner.averageStrength < avgOther ? '(房主更差)' : '(房主更好)'}`);
}
