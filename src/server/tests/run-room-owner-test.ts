/**
 * 运行房主位置偏差测试
 */

import { runRoomOwnerBiasTest, printRoomOwnerBiasResult } from './room-owner-bias-test';

async function main() {
  console.log('🎯 开始房主位置偏差测试...\n');
  
  const gameCount = 10000; // 运行1万局游戏
  
  console.log(`测试参数:`);
  console.log(`- 游戏局数: ${gameCount.toLocaleString()}`);
  console.log(`- 玩家数量: 4人`);
  console.log(`- 房主: RoomOwner (users[0])`);
  console.log(`- 测试内容: 模拟真实的座位轮转逻辑`);
  
  // 运行测试
  console.log('\n🔍 运行测试...');
  const stats = runRoomOwnerBiasTest(gameCount);
  
  // 打印结果
  printRoomOwnerBiasResult(stats);
  
  console.log('\n📋 结论:');
  console.log('如果房主的平均位置显著低于其他玩家，说明存在系统性的位置劣势。');
  console.log('在德州扑克中，位置越小越不利（小盲 < 大盲 < 第一位 < 庄家位）。');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { main as runRoomOwnerBiasTest };
