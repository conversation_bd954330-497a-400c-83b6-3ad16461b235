/**
 * 房主位置偏差测试
 * 模拟真实的房间逻辑，测试房主是否系统性地处于不利位置
 */

interface MockUser {
  token: string;
  name: string;
  isRoomOwner: boolean;
  isReady: boolean;
  isOffline: boolean;
  stack: number;
}

interface PositionStats {
  playerName: string;
  isRoomOwner: boolean;
  positionCounts: { [position: string]: number };
  totalGames: number;
  averagePosition: number; // 平均位置（0=小盲，1=大盲，2=庄家等）
}

/**
 * 模拟房间的座位轮转逻辑
 */
class MockRoom {
  users: string[] = [];
  userMap: { [token: string]: MockUser } = {};
  bigBlindUser: string = "";
  
  constructor(playerNames: string[]) {
    // 第一个玩家是房主
    playerNames.forEach((name, index) => {
      const token = `token_${index}`;
      this.users.push(token);
      this.userMap[token] = {
        token,
        name,
        isRoomOwner: index === 0,
        isReady: true,
        isOffline: false,
        stack: 1000
      };
    });
    
    // 房主是第一局大盲位
    this.bigBlindUser = this.users[0];
  }
  
  /**
   * 模拟nextGame()逻辑
   */
  nextGame(): void {
    const tokens = this.users;
    const currentBBIndex = tokens.findIndex((t) => t === this.bigBlindUser);
    
    // 寻找下一个可以做大盲位的玩家
    for (let i = 0; i < tokens.length - 1; ++i) {
      const bbToken = tokens[(currentBBIndex + i + 1) % tokens.length];
      const user = this.userMap[bbToken];
      if (user.isReady && user.stack >= 4) { // 假设小盲是2
        this.bigBlindUser = bbToken;
        return;
      }
    }
  }
  
  /**
   * 模拟sortUsersBySmallBlind()逻辑
   */
  sortUsersBySmallBlind(): string[] {
    let tokens = this.users.filter(
      (t) =>
        !this.userMap[t].isOffline &&
        this.userMap[t].isReady &&
        this.userMap[t].stack >= 4
    );
    
    if (tokens.length < 2) return [];
    
    const smallBlindIndex =
      (tokens.findIndex((t) => t === this.bigBlindUser) + (tokens.length - 1)) %
      tokens.length;
    
    return [
      ...tokens.slice(smallBlindIndex),
      ...tokens.slice(0, smallBlindIndex),
    ];
  }
  
  /**
   * 获取当前游戏的座位安排
   */
  getCurrentSeating(): { [token: string]: number } {
    const sortedUsers = this.sortUsersBySmallBlind();
    const seating: { [token: string]: number } = {};
    
    sortedUsers.forEach((token, index) => {
      seating[token] = index; // 0=小盲，1=大盲，2=第一个位置，3=庄家位等
    });
    
    return seating;
  }
}

/**
 * 运行房主位置偏差测试
 */
export function runRoomOwnerBiasTest(gameCount: number = 1000): PositionStats[] {
  const playerNames = ['RoomOwner', 'Player1', 'Player2', 'Player3'];
  const room = new MockRoom(playerNames);
  
  // 初始化统计
  const stats: PositionStats[] = playerNames.map((name, index) => ({
    playerName: name,
    isRoomOwner: index === 0,
    positionCounts: { '0': 0, '1': 0, '2': 0, '3': 0 }, // 小盲，大盲，第一个，庄家
    totalGames: 0,
    averagePosition: 0
  }));
  
  // 运行测试
  for (let game = 0; game < gameCount; game++) {
    const seating = room.getCurrentSeating();
    
    // 记录每个玩家的位置
    Object.entries(seating).forEach(([token, position]) => {
      const user = room.userMap[token];
      const playerStats = stats.find(s => s.playerName === user.name)!;
      
      playerStats.positionCounts[position.toString()]++;
      playerStats.totalGames++;
    });
    
    // 进入下一局
    room.nextGame();
  }
  
  // 计算平均位置
  stats.forEach(stat => {
    let totalPositionValue = 0;
    Object.entries(stat.positionCounts).forEach(([position, count]) => {
      totalPositionValue += parseInt(position) * count;
    });
    stat.averagePosition = totalPositionValue / stat.totalGames;
  });
  
  return stats;
}

/**
 * 打印测试结果
 */
export function printRoomOwnerBiasResult(stats: PositionStats[]): void {
  console.log('\n=== 房主位置偏差测试结果 ===');
  
  stats.forEach(stat => {
    console.log(`\n${stat.playerName} ${stat.isRoomOwner ? '(房主)' : ''}:`);
    console.log(`  总游戏数: ${stat.totalGames}`);
    console.log(`  平均位置: ${stat.averagePosition.toFixed(3)} (0=小盲最不利, 3=庄家最有利)`);
    console.log(`  位置分布:`);
    console.log(`    小盲位(0): ${stat.positionCounts['0']} (${(stat.positionCounts['0']/stat.totalGames*100).toFixed(1)}%)`);
    console.log(`    大盲位(1): ${stat.positionCounts['1']} (${(stat.positionCounts['1']/stat.totalGames*100).toFixed(1)}%)`);
    console.log(`    第一位(2): ${stat.positionCounts['2']} (${(stat.positionCounts['2']/stat.totalGames*100).toFixed(1)}%)`);
    console.log(`    庄家位(3): ${stat.positionCounts['3']} (${(stat.positionCounts['3']/stat.totalGames*100).toFixed(1)}%)`);
  });
  
  // 分析房主劣势
  const roomOwner = stats.find(s => s.isRoomOwner)!;
  const others = stats.filter(s => !s.isRoomOwner);
  const avgOthersPosition = others.reduce((sum, s) => sum + s.averagePosition, 0) / others.length;
  
  console.log(`\n=== 分析结果 ===`);
  console.log(`房主平均位置: ${roomOwner.averagePosition.toFixed(3)}`);
  console.log(`其他玩家平均位置: ${avgOthersPosition.toFixed(3)}`);
  
  const bias = roomOwner.averagePosition - avgOthersPosition;
  console.log(`位置偏差: ${bias.toFixed(3)} ${bias < 0 ? '(房主处于更不利位置!)' : '(房主处于更有利位置)'}`);
  
  if (Math.abs(bias) > 0.1) {
    console.log(`⚠️  检测到显著的位置偏差！这可能解释了房主输得更多的原因。`);
  } else {
    console.log(`✅ 位置分布相对公平。`);
  }
}
