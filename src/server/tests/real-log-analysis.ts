/**
 * 真实日志分析 - 专门分析jin作为房主的游戏数据
 */

import * as fs from 'fs';
import * as path from 'path';

interface GameResult {
  gameNumber: number;
  playerName: string;
  cards: string;
  result: 'win' | 'lose' | 'fold';
  profit: number;
  stackBefore: number;
  stackAfter: number;
  position?: string;
  handStrength: number;
}

interface PlayerSummary {
  name: string;
  isRoomOwner: boolean;
  totalGames: number;
  totalProfit: number;
  winCount: number;
  loseCount: number;
  foldCount: number;
  winRate: number;
  avgProfit: number;
  avgHandStrength: number;
  strongHands: number;
  mediumHands: number;
  weakHands: number;
  biggestWin: number;
  biggestLoss: number;
  foldRate: number;
}

/**
 * 计算手牌强度
 */
function calculateHandStrength(cards: string): number {
  const cleanCards = cards.replace(/【|】|♠|♥|♦|♣|︎/g, '');
  
  // 转换牌面值
  const convertCard = (c: string) => {
    if (c === 'A') return 14;
    if (c === 'K') return 13;
    if (c === 'Q') return 12;
    if (c === 'J') return 11;
    if (c === 'T') return 10;
    return parseInt(c) || 0;
  };
  
  const nums = cleanCards.match(/[AKQJT2-9]/g) || [];
  if (nums.length < 2) return 25;
  
  const card1 = convertCard(nums[0]);
  const card2 = convertCard(nums[1]);
  const high = Math.max(card1, card2);
  const low = Math.min(card1, card2);
  
  // 对子
  if (card1 === card2) {
    if (card1 >= 14) return 95; // AA
    if (card1 >= 13) return 90; // KK
    if (card1 >= 12) return 85; // QQ
    if (card1 >= 11) return 80; // JJ
    if (card1 >= 10) return 75; // TT
    return 60; // 小对子
  }
  
  // 同花检测
  const suits = cards.match(/[♠♥♦♣shdc]/g) || [];
  const isSuited = suits.length >= 2 && suits[0] === suits[1];
  
  // 高牌组合
  if (high === 14) { // A开头
    if (low >= 13) return isSuited ? 88 : 85; // AK
    if (low >= 12) return isSuited ? 82 : 78; // AQ
    if (low >= 11) return isSuited ? 75 : 70; // AJ
    if (low >= 10) return isSuited ? 68 : 62; // AT
    return isSuited ? 55 : 45; // A小牌
  }
  
  if (high >= 13 && low >= 12) return isSuited ? 70 : 65; // KQ
  if (high >= 13 && low >= 11) return isSuited ? 65 : 60; // KJ
  if (high >= 12 && low >= 11) return isSuited ? 62 : 58; // QJ
  
  // 连牌
  if (Math.abs(high - low) === 1 && low >= 8) {
    return isSuited ? 55 : 50;
  }
  
  // 其他组合
  if (isSuited && high >= 10) return 45;
  if (high >= 12) return 40;
  
  return 25; // 垃圾牌
}

/**
 * 解析真实日志文件
 */
function parseRealLogFile(): GameResult[] {
  const logPath = path.join(__dirname, '../../../poker-app_logs.txt');
  
  if (!fs.existsSync(logPath)) {
    console.error('日志文件不存在:', logPath);
    return [];
  }
  
  const logContent = fs.readFileSync(logPath, 'utf-8');
  const lines = logContent.split('\n');
  
  const results: GameResult[] = [];
  let gameNumber = 0;
  let isJinRoom = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测jin创建的房间
    if (line.includes('第一局游戏开始，大盲位: jin')) {
      isJinRoom = true;
      gameNumber = 0;
      continue;
    }
    
    // 如果不是jin的房间，跳过
    if (!isJinRoom) continue;
    
    // 检测新游戏开始
    if (line.includes('START: [')) {
      gameNumber++;
      continue;
    }
    
    // 解析游戏结果 - 修正正则表达式
    const winMatch = line.match(/^([^\s]+)\s+【(.+?)】.*win💰\s*(\d+)/);
    if (winMatch) {
      const [, playerName, cards, profit] = winMatch;
      if (playerName.length > 1) { // 过滤单字符
        const handStrength = calculateHandStrength(cards);

        results.push({
          gameNumber,
          playerName,
          cards,
          result: 'win',
          profit: parseInt(profit),
          stackBefore: 0,
          stackAfter: 0,
          handStrength
        });
      }
      continue;
    }

    const loseMatch = line.match(/^([^\s]+)\s+【(.+?)】.*lose\s*-(\d+)/);
    if (loseMatch) {
      const [, playerName, cards, loss] = loseMatch;
      if (playerName.length > 1) { // 过滤单字符
        const handStrength = calculateHandStrength(cards);

        results.push({
          gameNumber,
          playerName,
          cards,
          result: 'lose',
          profit: -parseInt(loss),
          stackBefore: 0,
          stackAfter: 0,
          handStrength
        });
      }
      continue;
    }

    // 解析弃牌 - 改进匹配
    const foldMatch = line.match(/USER Fold.*?([^\s]{2,})\s*$/);
    if (foldMatch) {
      const [, playerName] = foldMatch;
      if (playerName.length > 1) { // 过滤单字符
        results.push({
          gameNumber,
          playerName,
          cards: 'unknown',
          result: 'fold',
          profit: -2, // 假设小盲或大盲
          stackBefore: 0,
          stackAfter: 0,
          handStrength: 0
        });
      }
      continue;
    }
  }
  
  return results;
}

/**
 * 分析玩家数据
 */
function analyzePlayerData(results: GameResult[], playerName: string): PlayerSummary {
  const playerResults = results.filter(r => r.playerName === playerName);
  
  if (playerResults.length === 0) {
    return {
      name: playerName,
      isRoomOwner: false,
      totalGames: 0,
      totalProfit: 0,
      winCount: 0,
      loseCount: 0,
      foldCount: 0,
      winRate: 0,
      avgProfit: 0,
      avgHandStrength: 0,
      strongHands: 0,
      mediumHands: 0,
      weakHands: 0,
      biggestWin: 0,
      biggestLoss: 0,
      foldRate: 0
    };
  }
  
  const totalProfit = playerResults.reduce((sum, r) => sum + r.profit, 0);
  const winCount = playerResults.filter(r => r.result === 'win').length;
  const loseCount = playerResults.filter(r => r.result === 'lose').length;
  const foldCount = playerResults.filter(r => r.result === 'fold').length;
  
  const nonFoldResults = playerResults.filter(r => r.result !== 'fold');
  const avgHandStrength = nonFoldResults.length > 0 
    ? nonFoldResults.reduce((sum, r) => sum + r.handStrength, 0) / nonFoldResults.length 
    : 0;
  
  const strongHands = nonFoldResults.filter(r => r.handStrength >= 70).length;
  const mediumHands = nonFoldResults.filter(r => r.handStrength >= 50 && r.handStrength < 70).length;
  const weakHands = nonFoldResults.filter(r => r.handStrength < 50).length;
  
  const profits = playerResults.map(r => r.profit);
  const biggestWin = Math.max(...profits);
  const biggestLoss = Math.min(...profits);
  
  return {
    name: playerName,
    isRoomOwner: playerName === 'jin',
    totalGames: playerResults.length,
    totalProfit,
    winCount,
    loseCount,
    foldCount,
    winRate: (winCount + loseCount) > 0 ? winCount / (winCount + loseCount) : 0,
    avgProfit: totalProfit / playerResults.length,
    avgHandStrength,
    strongHands,
    mediumHands,
    weakHands,
    biggestWin,
    biggestLoss,
    foldRate: playerResults.length > 0 ? foldCount / playerResults.length : 0
  };
}

/**
 * 生成详细报告
 */
export function generateRealLogReport(): void {
  console.log('🎯 基于真实日志的详细分析报告');
  console.log('=' .repeat(80));
  
  const results = parseRealLogFile();
  
  if (results.length === 0) {
    console.log('❌ 无法解析日志数据');
    return;
  }
  
  console.log(`📊 解析到 ${results.length} 条游戏记录`);
  
  // 获取所有玩家
  const allPlayers = [...new Set(results.map(r => r.playerName))];
  console.log(`👥 参与玩家: ${allPlayers.join(', ')}`);
  
  console.log('\n📈 详细统计分析:');
  console.log('-'.repeat(80));
  
  const analyses: PlayerSummary[] = [];
  
  allPlayers.forEach(playerName => {
    const analysis = analyzePlayerData(results, playerName);
    if (analysis.totalGames === 0) return;
    
    analyses.push(analysis);
    
    console.log(`\n${analysis.name} ${analysis.isRoomOwner ? '👑 (房主)' : ''}:`);
    console.log(`  总参与局数: ${analysis.totalGames}`);
    console.log(`  总盈亏: ${analysis.totalProfit > 0 ? '+' : ''}${analysis.totalProfit}`);
    console.log(`  胜/负/弃: ${analysis.winCount}/${analysis.loseCount}/${analysis.foldCount}`);
    console.log(`  胜率: ${(analysis.winRate * 100).toFixed(1)}% (不含弃牌)`);
    console.log(`  弃牌率: ${(analysis.foldRate * 100).toFixed(1)}%`);
    console.log(`  平均每局盈亏: ${analysis.avgProfit.toFixed(2)}`);
    console.log(`  平均牌力: ${analysis.avgHandStrength.toFixed(1)}`);
    console.log(`  牌力分布: 强牌${analysis.strongHands} 中等${analysis.mediumHands} 弱牌${analysis.weakHands}`);
    console.log(`  最大单次盈利: +${analysis.biggestWin}`);
    console.log(`  最大单次亏损: ${analysis.biggestLoss}`);
  });
  
  // 房主对比分析
  const roomOwner = analyses.find(a => a.isRoomOwner);
  const others = analyses.filter(a => !a.isRoomOwner);
  
  if (roomOwner && others.length > 0) {
    console.log('\n🔍 房主 vs 其他玩家深度对比:');
    console.log('-'.repeat(80));
    
    const avgOtherProfit = others.reduce((sum, a) => sum + a.avgProfit, 0) / others.length;
    const avgOtherHandStrength = others.reduce((sum, a) => sum + a.avgHandStrength, 0) / others.length;
    const avgOtherWinRate = others.reduce((sum, a) => sum + a.winRate, 0) / others.length;
    const avgOtherFoldRate = others.reduce((sum, a) => sum + a.foldRate, 0) / others.length;
    
    console.log(`💰 平均盈亏对比:`);
    console.log(`   房主: ${roomOwner.avgProfit.toFixed(2)}`);
    console.log(`   其他: ${avgOtherProfit.toFixed(2)}`);
    console.log(`   差异: ${(roomOwner.avgProfit - avgOtherProfit).toFixed(2)} ${roomOwner.avgProfit < avgOtherProfit ? '❌ 房主更差' : '✅ 房主更好'}`);
    
    console.log(`\n🃏 牌力对比:`);
    console.log(`   房主: ${roomOwner.avgHandStrength.toFixed(1)}`);
    console.log(`   其他: ${avgOtherHandStrength.toFixed(1)}`);
    console.log(`   差异: ${(roomOwner.avgHandStrength - avgOtherHandStrength).toFixed(1)} ${roomOwner.avgHandStrength < avgOtherHandStrength ? '❌ 房主牌更差' : '✅ 房主牌更好'}`);
    
    console.log(`\n🏆 胜率对比:`);
    console.log(`   房主: ${(roomOwner.winRate * 100).toFixed(1)}%`);
    console.log(`   其他: ${(avgOtherWinRate * 100).toFixed(1)}%`);
    console.log(`   差异: ${((roomOwner.winRate - avgOtherWinRate) * 100).toFixed(1)}% ${roomOwner.winRate < avgOtherWinRate ? '❌ 房主胜率更低' : '✅ 房主胜率更高'}`);
    
    console.log(`\n🚫 弃牌率对比:`);
    console.log(`   房主: ${(roomOwner.foldRate * 100).toFixed(1)}%`);
    console.log(`   其他: ${(avgOtherFoldRate * 100).toFixed(1)}%`);
    console.log(`   差异: ${((roomOwner.foldRate - avgOtherFoldRate) * 100).toFixed(1)}% ${roomOwner.foldRate > avgOtherFoldRate ? '⚠️ 房主弃牌更多' : '✅ 房主弃牌正常'}`);
  }
  
  console.log('\n📋 分析结论:');
  console.log('-'.repeat(80));
  
  if (roomOwner) {
    if (roomOwner.avgProfit < -10) {
      console.log('❌ 房主存在严重的盈利劣势，平均每局亏损超过10');
    } else if (roomOwner.avgProfit < -5) {
      console.log('⚠️  房主存在明显的盈利劣势，平均每局亏损5-10');
    } else if (roomOwner.avgProfit < 0) {
      console.log('⚠️  房主存在轻微的盈利劣势');
    } else {
      console.log('✅ 房主盈利情况正常');
    }
    
    if (roomOwner.foldRate > 0.7) {
      console.log('❌ 房主弃牌率过高，可能过于保守');
    } else if (roomOwner.foldRate > 0.5) {
      console.log('⚠️  房主弃牌率偏高');
    } else {
      console.log('✅ 房主弃牌率正常');
    }
    
    if (roomOwner.avgHandStrength < 35) {
      console.log('❌ 房主平均牌力显著偏低，可能存在发牌问题');
    } else if (roomOwner.avgHandStrength < 40) {
      console.log('⚠️  房主平均牌力略低');
    } else {
      console.log('✅ 房主牌力分布正常');
    }
  }
  
  console.log('\n🔧 修复建议:');
  console.log('-'.repeat(80));
  console.log('1. ✅ 已实现发牌顺序随机化');
  console.log('2. 📊 建议添加实时统计监控');
  console.log('3. 🎯 建议为房主提供策略指导');
  console.log('4. 🔍 建议定期进行公平性审计');
}

// 运行分析
if (require.main === module) {
  generateRealLogReport();
}
