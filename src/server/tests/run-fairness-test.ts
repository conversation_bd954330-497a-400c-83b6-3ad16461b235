/**
 * 运行发牌公平性测试
 * 对比修复前后的发牌公平性
 */

import { runFairnessTest, printTestResult } from './fairness-test';

async function main() {
  console.log('🎯 开始发牌公平性测试...\n');
  
  const gameCount = 50000; // 运行5万局游戏
  const playerCount = 4;   // 4人游戏
  
  console.log(`测试参数:`);
  console.log(`- 游戏局数: ${gameCount.toLocaleString()}`);
  console.log(`- 玩家数量: ${playerCount}`);
  console.log(`- 房主位置: 0 (users[0])`);
  
  // 测试修复前的版本（固定发牌顺序）
  console.log('\n🔍 测试修复前版本（固定发牌顺序）...');
  const beforeResult = runFairnessTest(gameCount, playerCount, false);
  printTestResult(beforeResult, '修复前测试结果');
  
  // 测试修复后的版本（随机发牌顺序）
  console.log('\n🔍 测试修复后版本（随机发牌顺序）...');
  const afterResult = runFairnessTest(gameCount, playerCount, true);
  printTestResult(afterResult, '修复后测试结果');
  
  // 对比分析
  console.log('\n📊 对比分析:');
  const beforeOwner = beforeResult.roomOwnerStats;
  const afterOwner = afterResult.roomOwnerStats;
  
  const beforeOthers = beforeResult.players.filter(p => p.name !== 'RoomOwner');
  const afterOthers = afterResult.players.filter(p => p.name !== 'RoomOwner');
  
  const beforeOtherAvg = beforeOthers.reduce((sum, p) => sum + p.averageStrength, 0) / beforeOthers.length;
  const afterOtherAvg = afterOthers.reduce((sum, p) => sum + p.averageStrength, 0) / afterOthers.length;
  
  const beforeDiff = beforeOwner.averageStrength - beforeOtherAvg;
  const afterDiff = afterOwner.averageStrength - afterOtherAvg;
  
  console.log(`修复前房主劣势: ${Math.abs(beforeDiff).toFixed(2)}分 ${beforeDiff < 0 ? '(确实更差)' : '(实际更好)'}`);
  console.log(`修复后房主劣势: ${Math.abs(afterDiff).toFixed(2)}分 ${afterDiff < 0 ? '(仍然更差)' : '(已经公平)'}`);
  
  const improvement = Math.abs(beforeDiff) - Math.abs(afterDiff);
  console.log(`改善程度: ${improvement.toFixed(2)}分 ${improvement > 0 ? '✅ 有改善' : '❌ 无改善'}`);
  
  console.log(`\n公平性分数对比:`);
  console.log(`修复前: ${beforeResult.fairnessScore.toFixed(3)}`);
  console.log(`修复后: ${afterResult.fairnessScore.toFixed(3)}`);
  console.log(`改善: ${(afterResult.fairnessScore - beforeResult.fairnessScore).toFixed(3)}`);
  
  // 强牌分布对比
  console.log(`\n🎴 强牌分布对比:`);
  console.log(`修复前 - 房主强牌率: ${(beforeOwner.strongHands / beforeOwner.totalGames * 100).toFixed(1)}%`);
  console.log(`修复前 - 其他玩家强牌率: ${(beforeOthers.reduce((sum, p) => sum + p.strongHands, 0) / (beforeOthers.length * beforeOthers[0].totalGames) * 100).toFixed(1)}%`);
  
  console.log(`修复后 - 房主强牌率: ${(afterOwner.strongHands / afterOwner.totalGames * 100).toFixed(1)}%`);
  console.log(`修复后 - 其他玩家强牌率: ${(afterOthers.reduce((sum, p) => sum + p.strongHands, 0) / (afterOthers.length * afterOthers[0].totalGames) * 100).toFixed(1)}%`);
  
  // 结论
  console.log(`\n📋 测试结论:`);
  if (improvement > 1) {
    console.log(`✅ 修复成功！房主的牌力劣势减少了${improvement.toFixed(2)}分`);
  } else if (improvement > 0) {
    console.log(`⚠️  有轻微改善，但可能需要进一步优化`);
  } else {
    console.log(`❌ 修复效果不明显，需要重新检查算法`);
  }
  
  if (afterResult.fairnessScore > 0.95) {
    console.log(`✅ 发牌公平性优秀 (${afterResult.fairnessScore.toFixed(3)})`);
  } else if (afterResult.fairnessScore > 0.9) {
    console.log(`⚠️  发牌公平性良好 (${afterResult.fairnessScore.toFixed(3)})`);
  } else {
    console.log(`❌ 发牌公平性仍需改进 (${afterResult.fairnessScore.toFixed(3)})`);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { main as runFairnessComparison };
